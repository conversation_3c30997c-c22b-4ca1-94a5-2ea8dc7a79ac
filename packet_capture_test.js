/**
 * 抓包检测功能测试脚本
 * 用于测试抓包检测模块的各项功能
 */

"ui";

// 简化版的日志模块（用于测试）
const TestLogModule = {
    log: function(message, level) {
        level = level || "INFO";
        var logMessage = "[" + level + "] " + message;
        console.log(logMessage);
        
        // 在UI上显示日志
        try {
            if (typeof ui !== 'undefined' && ui.logText) {
                ui.run(function() {
                    var currentText = ui.logText.getText().toString();
                    var newText = currentText + "\n" + logMessage;
                    // 限制日志长度，避免内存问题
                    var lines = newText.split('\n');
                    if (lines.length > 100) {
                        lines = lines.slice(-100);
                        newText = lines.join('\n');
                    }
                    ui.logText.setText(newText);
                });
            }
        } catch (e) {
            console.error("更新UI日志失败: " + e.message);
        }
    },
    
    clear: function() {
        try {
            if (typeof ui !== 'undefined' && ui.logText) {
                ui.run(function() {
                    ui.logText.setText("日志已清空，等待开始检测...");
                });
            }
        } catch (e) {
            console.error("清空UI日志失败: " + e.message);
        }
    }
};

// 模拟抓包检测模块（从main.js复制）
const PacketCaptureDetector = (function () {
    return {
        /**
         * 检测是否开启了抓包工具
         * @returns {boolean} 如果检测到抓包工具返回true，否则返回false
         */
        detectPacketCapture: function () {
            try {
                TestLogModule.log("开始检测抓包工具", "INFO");

                // 检测抓包工具进程（主要且唯一的方法）
                var processDetected = this.checkCaptureProcesses();
                if (processDetected) {
                    TestLogModule.log("检测到抓包工具，存在抓包风险", "WARN");
                    return true;
                }

                TestLogModule.log("未检测到抓包工具", "INFO");
                return false;
            } catch (e) {
                TestLogModule.log("抓包工具检测过程中发生错误: " + e.message, "ERROR");
                // 出现错误时为了安全起见，假设存在抓包
                return true;
            }
        },

        /**
         * 在发送业务请求前进行抓包工具检测
         * @param {string} targetUrl - 目标URL
         * @returns {boolean} 如果检测到抓包工具返回true，否则返回false
         */
        detectBeforeRequest: function (targetUrl) {
            try {
                TestLogModule.log("准备访问: " + targetUrl, "INFO");
                TestLogModule.log("开始请求前抓包工具检测...", "INFO");

                // 快速检测抓包工具进程
                var captureDetected = this.quickCaptureCheck();
                if (captureDetected) {
                    TestLogModule.log("检测到抓包工具，禁止访问: " + targetUrl, "WARN");
                    return true;
                }

                TestLogModule.log("抓包工具检测通过，允许访问: " + targetUrl, "INFO");
                return false;
            } catch (e) {
                TestLogModule.log("请求前检测失败: " + e.message, "ERROR");
                // 检测失败时为了安全起见，假设存在抓包
                return true;
            }
        },

        /**
         * 快速抓包工具检测（用于请求前检测）
         * @returns {boolean}
         */
        quickCaptureCheck: function () {
            try {
                TestLogModule.log("快速检测抓包工具进程...", "INFO");
                
                // 只检测最常见的抓包工具，提高速度
                var commonCaptureTools = [
                    "charles", "com.xk72.charles",
                    "fiddler", "com.telerik.fiddler", 
                    "httpcanary", "com.guoshi.httpcanary",
                    "packetcapture", "app.greyshirts.sslcapture",
                    "reqable", "com.reqable.android", "com.reqable.macos"
                ];
                
                // 快速进程检测
                var psResult = shell("ps", true);
                if (psResult && psResult.code === 0 && psResult.result) {
                    var processes = psResult.result.toString().toLowerCase();
                    
                    for (var i = 0; i < commonCaptureTools.length; i++) {
                        var toolName = commonCaptureTools[i].toLowerCase();
                        if (processes.includes(toolName)) {
                            TestLogModule.log("快速检测到抓包工具: " + toolName, "WARN");
                            
                            // 直接强制结束该进程
                            this.forceKillProcess(toolName);
                            
                            return true;
                        }
                    }
                }

                return false;
            } catch (e) {
                TestLogModule.log("快速抓包工具检测异常: " + e.message, "ERROR");
                return false;
            }
        },

        /**
         * 检测运行中的抓包工具进程
         * @returns {boolean}
         */
        checkCaptureProcesses: function () {
            try {
                TestLogModule.log("开始检测抓包工具进程...", "INFO");
                
                // 纯抓包工具进程名列表
                var captureTools = [
                    // 桌面端抓包工具
                    "charles", "charlesproxy", "com.xk72.charles",
                    "fiddler", "telerik.fiddler", "com.telerik.fiddler",
                    "wireshark", "org.wireshark.app",
                    "burpsuite", "burp", "portswigger.burp",
                    "mitmproxy", "mitm", "mitmweb",
                    "proxyman", "com.proxyman.macos",
                    "reqable", "com.reqable.macos", "com.reqable.windows",
                    
                    // 移动端抓包工具
                    "httpcanary", "com.guoshi.httpcanary",
                    "packetcapture", "app.greyshirts.sslcapture",
                    "httpdebugger", "com.itkacher.okhttpprofiler",
                    "networktester", "ru.gavrikov.networktools",
                    "sslkillswitch", "com.nablac0d3.sslkillswitch2",
                    "reqable", "com.reqable.android",
                    
                    // 网络抓包分析工具
                    "tcpdump", "tshark",
                    "ettercap", "bettercap"
                ];
                
                var detected = false;
                var detectedTools = [];
                var processedTools = []; // 记录已处理的工具，避免重复结束进程
                
                // 方法1：使用ps命令检测进程
                try {
                    TestLogModule.log("使用ps命令检测进程...", "INFO");
                    var psResult = shell("ps", true);
                    if (psResult && psResult.code === 0 && psResult.result) {
                        var processes = psResult.result.toString().toLowerCase();
                        TestLogModule.log("进程列表长度: " + processes.length + " 字符", "INFO");
                        
                        for (var i = 0; i < captureTools.length; i++) {
                            var toolName = captureTools[i].toLowerCase();
                            if (processes.includes(toolName)) {
                                TestLogModule.log("检测到抓包工具进程: " + toolName, "WARN");
                                detectedTools.push(toolName);
                                detected = true;
                                
                                // 避免重复处理同一个工具
                                if (processedTools.indexOf(toolName) === -1) {
                                    processedTools.push(toolName);
                                    this.forceKillProcess(toolName);
                                } else {
                                    TestLogModule.log("跳过重复处理: " + toolName, "INFO");
                                }
                            }
                        }
                    }
                } catch (e) {
                    TestLogModule.log("ps命令检测异常: " + e.message, "ERROR");
                }
                
                // 方法2：检测正在运行的抓包应用（而不是已安装的）
                try {
                    TestLogModule.log("检测正在运行的抓包应用...", "INFO");
                    
                    // 使用dumpsys activity检测正在运行的应用
                    var runningAppsResult = shell("dumpsys activity activities | grep 'Run #'", true);
                    if (runningAppsResult && runningAppsResult.code === 0 && runningAppsResult.result) {
                        var runningApps = runningAppsResult.result.toString().toLowerCase();
                        TestLogModule.log("正在运行的应用信息长度: " + runningApps.length + " 字符", "INFO");
                        
                        for (var i = 0; i < captureTools.length; i++) {
                            var toolName = captureTools[i].toLowerCase();
                            if (toolName.includes("com.") && runningApps.includes(toolName)) {
                                TestLogModule.log("检测到正在运行的抓包应用: " + toolName, "WARN");
                                detectedTools.push(toolName);
                                detected = true;
                                
                                // 避免重复处理同一个工具
                                if (processedTools.indexOf(toolName) === -1) {
                                    processedTools.push(toolName);
                                    this.forceKillProcess(toolName);
                                } else {
                                    TestLogModule.log("跳过重复处理: " + toolName, "INFO");
                                }
                            }
                        }
                    }
                    
                    // 补充检查：使用am stack list检测前台应用
                    var stackResult = shell("am stack list", true);
                    if (stackResult && stackResult.code === 0 && stackResult.result) {
                        var stackInfo = stackResult.result.toString().toLowerCase();
                        
                        for (var i = 0; i < captureTools.length; i++) {
                            var toolName = captureTools[i].toLowerCase();
                            if (toolName.includes("com.") && stackInfo.includes(toolName)) {
                                TestLogModule.log("检测到前台抓包应用: " + toolName, "WARN");
                                if (detectedTools.indexOf(toolName) === -1) {
                                    detectedTools.push(toolName);
                                    detected = true;
                                    
                                    if (processedTools.indexOf(toolName) === -1) {
                                        processedTools.push(toolName);
                                        this.killCaptureProcess(toolName);
                                    }
                                }
                            }
                        }
                    }
                    
                } catch (e) {
                    TestLogModule.log("运行应用检测异常: " + e.message, "ERROR");
                }
                
                // 方法3：使用top命令检测活跃进程
                try {
                    TestLogModule.log("使用top命令检测活跃进程...", "INFO");
                    var topResult = shell("top -n 1", true);
                    if (topResult && topResult.code === 0 && topResult.result) {
                        var topProcesses = topResult.result.toString().toLowerCase();
                        
                        for (var i = 0; i < captureTools.length; i++) {
                            var toolName = captureTools[i].toLowerCase();
                            if (topProcesses.includes(toolName)) {
                                TestLogModule.log("检测到活跃抓包进程: " + toolName, "WARN");
                                if (detectedTools.indexOf(toolName) === -1) {
                                    detectedTools.push(toolName);
                                }
                                detected = true;
                                
                                // 避免重复处理同一个工具
                                if (processedTools.indexOf(toolName) === -1) {
                                    processedTools.push(toolName);
                                    this.forceKillProcess(toolName);
                                } else {
                                    TestLogModule.log("跳过重复处理: " + toolName, "INFO");
                                }
                            }
                        }
                    }
                } catch (e) {
                    TestLogModule.log("top命令检测异常: " + e.message, "ERROR");
                }
                
                // 方法4：检测抓包工具专用端口
                try {
                    TestLogModule.log("检测抓包工具专用端口...", "INFO");
                    var capturePorts = [
                        "8080",  // Charles默认端口
                        "8888",  // Fiddler默认端口
                        "8443",  // HTTPS抓包端口
                        "9090",  // 其他抓包工具常用端口
                        "9000"   // Reqable默认端口
                    ];
                    
                    for (var i = 0; i < capturePorts.length; i++) {
                        var port = capturePorts[i];
                        var netstatResult = shell("netstat -an | grep :" + port, true);
                        if (netstatResult && netstatResult.code === 0 && netstatResult.result) {
                            var netstatOutput = netstatResult.result.toString();
                            if (netstatOutput.trim() !== "" && netstatOutput.includes("LISTEN")) {
                                TestLogModule.log("检测到抓包工具端口 " + port + " 正在监听", "WARN");
                                detected = true;
                            }
                        }
                    }
                } catch (e) {
                    TestLogModule.log("抓包端口检测异常: " + e.message, "ERROR");
                }
                
                if (detected) {
                    TestLogModule.log("检测到抓包工具: " + detectedTools.join(", "), "WARN");
                } else {
                    TestLogModule.log("未检测到抓包工具进程", "INFO");
                }
                
                return detected;
            } catch (e) {
                TestLogModule.log("进程检测发生严重错误: " + e.message, "ERROR");
                return false;
            }
        },



        /**
         * 强制结束抓包应用进程
         * @param {string} toolName - 抓包工具名称
         */
        forceKillProcess: function (toolName) {
            try {
                TestLogModule.log("🔥 强制结束抓包应用: " + toolName, "WARN");
                
                // 如果是Android应用包名，使用am force-stop
                if (toolName.includes("com.") || toolName.includes("app.")) {
                    try {
                        var forceStopResult = shell("am force-stop " + toolName, true);
                        if (forceStopResult && forceStopResult.code === 0) {
                            TestLogModule.log("✅ 强制停止应用成功: " + toolName, "INFO");
                        } else {
                            TestLogModule.log("⚠️ 强制停止应用可能失败: " + toolName, "WARN");
                        }
                    } catch (e) {
                        TestLogModule.log("强制停止应用异常: " + e.message, "ERROR");
                    }
                } else {
                    // 如果是进程名，使用pkill强制结束
                    try {
                        var pkillResult = shell("pkill -9 -f " + toolName, true);
                        if (pkillResult && pkillResult.code === 0) {
                            TestLogModule.log("✅ 强制结束进程成功: " + toolName, "INFO");
                        } else {
                            TestLogModule.log("⚠️ 强制结束进程可能失败: " + toolName, "WARN");
                        }
                    } catch (e) {
                        TestLogModule.log("强制结束进程异常: " + e.message, "ERROR");
                    }
                }
                
                // 简单验证（1秒后）
                setTimeout(function() {
                    try {
                        var checkResult = shell("pgrep -f " + toolName, true);
                        if (checkResult && checkResult.code === 0 && checkResult.result && checkResult.result.trim() !== "") {
                            TestLogModule.log("⚠️ " + toolName + " 可能仍在运行", "WARN");
                        } else {
                            TestLogModule.log("✅ " + toolName + " 已成功结束", "INFO");
                        }
                    } catch (e) {
                        TestLogModule.log("验证进程状态异常: " + e.message, "ERROR");
                    }
                }, 1000);
                
            } catch (e) {
                TestLogModule.log("强制结束抓包应用时发生错误: " + e.message, "ERROR");
            }
        },

        /**
         * 快速抓包工具检测（用于请求前检测）
         * @returns {boolean}
         */
        quickNetworkCheck: function () {
            try {
                // 改为快速抓包工具检测
                return this.quickCaptureCheck();
            } catch (e) {
                TestLogModule.log("快速抓包工具检测异常: " + e.message, "ERROR");
                return false;
            }
        },

        /**
         * 安全访问URL（带抓包检测）
         * @param {string} url - 要访问的URL
         * @returns {Object} 请求结果
         */
        safeRequest: function (url) {
            try {
                // 请求前检测
                var captureDetected = this.detectBeforeRequest(url);
                if (captureDetected) {
                    TestLogModule.log("检测到抓包工具，拒绝访问: " + url, "ERROR");
                    return {
                        success: false,
                        error: "检测到抓包工具，为了安全拒绝访问",
                        data: null
                    };
                }

                // 发送实际请求
                TestLogModule.log("开始安全访问: " + url, "INFO");
                var response = http.get(url, {
                    headers: {
                        "User-Agent": "Mozilla/5.0 (Android; Mobile) SafeBrowser/1.0"
                    },
                    timeout: 10000
                });

                if (response && response.statusCode === 200) {
                    TestLogModule.log("安全访问成功: " + url, "INFO");
                    return {
                        success: true,
                        error: null,
                        data: response.body.string()
                    };
                } else {
                    TestLogModule.log("访问失败，状态码: " + (response ? response.statusCode : "无响应"), "ERROR");
                    return {
                        success: false,
                        error: "网络请求失败",
                        data: null
                    };
                }
            } catch (e) {
                TestLogModule.log("安全访问异常: " + e.message, "ERROR");
                return {
                    success: false,
                    error: e.message,
                    data: null
                };
            }
        },

        /**
         * 通过网络请求检测抓包
         * @returns {boolean}
         */
        checkNetworkRequest: function () {
            try {
                TestLogModule.log("开始网络请求检测...", "INFO");
                var detected = false;

                // 检测方法1：发送HTTP请求并分析响应头
                try {
                    TestLogModule.log("发送HTTP测试请求...", "INFO");
                    var response = http.get("http://httpbin.org/get", {
                        headers: {
                            "User-Agent": "PacketCaptureDetector/1.0"
                        },
                        timeout: 5000
                    });
                    
                    if (response && response.statusCode === 200) {
                        var responseText = response.body.string();
                        TestLogModule.log("HTTP请求响应: " + responseText.substring(0, 200) + "...", "INFO");
                        
                        // 检查响应中是否包含代理相关信息
                        if (responseText.includes("X-Forwarded-For") || 
                            responseText.includes("X-Real-IP") ||
                            responseText.includes("Via:") ||
                            responseText.includes("Proxy-Connection")) {
                            TestLogModule.log("HTTP响应中检测到代理头信息", "WARN");
                            detected = true;
                        }
                        
                        // 解析JSON响应检查headers
                        try {
                            var jsonResponse = JSON.parse(responseText);
                            if (jsonResponse.headers) {
                                var headers = jsonResponse.headers;
                                TestLogModule.log("请求头信息: " + JSON.stringify(headers), "INFO");
                                
                                // 检查是否有代理相关的请求头
                                if (headers["X-Forwarded-For"] || 
                                    headers["X-Real-IP"] ||
                                    headers["Via"] ||
                                    headers["Proxy-Connection"]) {
                                    TestLogModule.log("请求头中检测到代理信息", "WARN");
                                    detected = true;
                                }
                            }
                        } catch (e) {
                            TestLogModule.log("解析HTTP响应JSON失败: " + e.message, "ERROR");
                        }
                    } else {
                        TestLogModule.log("HTTP请求失败，状态码: " + (response ? response.statusCode : "无响应"), "ERROR");
                    }
                } catch (e) {
                    TestLogModule.log("HTTP请求检测异常: " + e.message, "ERROR");
                }

                // 检测方法2：发送HTTPS请求检测SSL代理
                try {
                    TestLogModule.log("发送HTTPS测试请求...", "INFO");
                    var httpsResponse = http.get("https://httpbin.org/get", {
                        headers: {
                            "User-Agent": "PacketCaptureDetector/1.0"
                        },
                        timeout: 5000
                    });
                    
                    if (httpsResponse && httpsResponse.statusCode === 200) {
                        TestLogModule.log("HTTPS请求成功，状态码: " + httpsResponse.statusCode, "INFO");
                        
                        // 检查HTTPS请求是否被代理拦截
                        var httpsResponseText = httpsResponse.body.string();
                        if (httpsResponseText.includes("proxy") || 
                            httpsResponseText.includes("intercepted") ||
                            httpsResponseText.includes("mitm")) {
                            TestLogModule.log("HTTPS响应中检测到代理拦截迹象", "WARN");
                            detected = true;
                        }
                    } else {
                        TestLogModule.log("HTTPS请求失败，可能被代理拦截", "WARN");
                        // HTTPS请求失败可能表示SSL被拦截
                        detected = true;
                    }
                } catch (e) {
                    TestLogModule.log("HTTPS请求检测异常: " + e.message, "ERROR");
                    // HTTPS请求异常可能表示SSL证书问题（代理拦截）
                    if (e.message.includes("certificate") || 
                        e.message.includes("SSL") ||
                        e.message.includes("TLS")) {
                        TestLogModule.log("检测到SSL/TLS异常，可能存在HTTPS代理", "WARN");
                        detected = true;
                    }
                }

                // 检测方法3：检查网络延迟异常
                try {
                    TestLogModule.log("检测网络延迟...", "INFO");
                    var startTime = Date.now();
                    var pingResponse = http.get("http://www.google.com", {
                        timeout: 3000
                    });
                    var endTime = Date.now();
                    var latency = endTime - startTime;
                    
                    TestLogModule.log("网络延迟: " + latency + "ms", "INFO");
                    
                    // 如果延迟异常高，可能存在代理
                    if (latency > 5000) {
                        TestLogModule.log("网络延迟异常，可能存在代理", "WARN");
                        detected = true;
                    }
                } catch (e) {
                    TestLogModule.log("网络延迟检测异常: " + e.message, "ERROR");
                }

                // 检测方法4：检查DNS解析异常
                try {
                    TestLogModule.log("检测DNS解析...", "INFO");
                    
                    // 尝试解析一个应该不存在的域名
                    var dnsResponse = http.get("http://nonexistent-domain-for-test.com", {
                        timeout: 2000
                    });
                    
                    // 如果不存在的域名有响应，可能被代理拦截
                    if (dnsResponse && dnsResponse.statusCode !== -1) {
                        TestLogModule.log("不存在域名有响应，可能存在DNS代理", "WARN");
                        detected = true;
                    }
                } catch (e) {
                    TestLogModule.log("DNS解析检测正常: " + e.message, "INFO");
                    // DNS解析失败是正常的
                }

                if (detected) {
                    TestLogModule.log("网络请求检测发现抓包迹象", "WARN");
                } else {
                    TestLogModule.log("网络请求检测未发现抓包迹象", "INFO");
                }
                
                return detected;
            } catch (e) {
                TestLogModule.log("网络请求检测发生严重错误: " + e.message, "ERROR");
                return false;
            }
        },

        /**
         * 检测代理设置 - 网络抓包的主要检测方法
         * @returns {boolean}
         */
        checkProxySettings: function () {
            try {
                TestLogModule.log("检测网络代理设置...", "INFO");
                var detected = false;

                // 方法1：检测HTTP代理设置
                try {
                    var result = shell("settings get global http_proxy", true);
                    TestLogModule.log("HTTP代理检测结果: " + JSON.stringify(result), "INFO");
                    if (result && result.code === 0 && result.result) {
                        var proxyInfo = result.result.toString().trim();
                        TestLogModule.log("HTTP代理原始结果: '" + proxyInfo + "'", "INFO");
                        if (proxyInfo && proxyInfo !== "" && proxyInfo !== "null" && proxyInfo !== ":0") {
                            TestLogModule.log("检测到HTTP代理设置: " + proxyInfo, "WARN");
                            detected = true;
                        }
                    }
                } catch (e) {
                    TestLogModule.log("HTTP代理检测异常: " + e.message, "ERROR");
                }

                // 方法2：检测HTTPS代理设置
                try {
                    var httpsResult = shell("settings get global https_proxy", true);
                    TestLogModule.log("HTTPS代理检测结果: " + JSON.stringify(httpsResult), "INFO");
                    if (httpsResult && httpsResult.code === 0 && httpsResult.result) {
                        var httpsProxyInfo = httpsResult.result.toString().trim();
                        TestLogModule.log("HTTPS代理原始结果: '" + httpsProxyInfo + "'", "INFO");
                        if (httpsProxyInfo && httpsProxyInfo !== "" && httpsProxyInfo !== "null" && httpsProxyInfo !== ":0") {
                            TestLogModule.log("检测到HTTPS代理设置: " + httpsProxyInfo, "WARN");
                            detected = true;
                        }
                    }
                } catch (e) {
                    TestLogModule.log("HTTPS代理检测异常: " + e.message, "ERROR");
                }

                // 方法3：检测系统属性中的代理设置
                try {
                    var httpProxy = shell("getprop net.http_proxy", true);
                    TestLogModule.log("系统代理属性检测结果: " + JSON.stringify(httpProxy), "INFO");
                    if (httpProxy && httpProxy.code === 0 && httpProxy.result) {
                        var proxyResult = httpProxy.result.toString().trim();
                        TestLogModule.log("系统代理属性原始结果: '" + proxyResult + "'", "INFO");
                        if (proxyResult && proxyResult !== "" && proxyResult !== ":0") {
                            TestLogModule.log("检测到系统代理属性: " + proxyResult, "WARN");
                            detected = true;
                        }
                    }
                } catch (e) {
                    TestLogModule.log("系统代理属性检测异常: " + e.message, "ERROR");
                }

                // 方法4：检测全局代理主机
                try {
                    var globalProxy = shell("settings get global global_http_proxy_host", true);
                    TestLogModule.log("全局代理主机检测结果: " + JSON.stringify(globalProxy), "INFO");
                    if (globalProxy && globalProxy.code === 0 && globalProxy.result) {
                        var globalProxyHost = globalProxy.result.toString().trim();
                        TestLogModule.log("全局代理主机原始结果: '" + globalProxyHost + "'", "INFO");
                        if (globalProxyHost && globalProxyHost !== "" && globalProxyHost !== "null") {
                            TestLogModule.log("检测到全局代理主机: " + globalProxyHost, "WARN");
                            detected = true;
                        }
                    }
                } catch (e) {
                    TestLogModule.log("全局代理主机检测异常: " + e.message, "ERROR");
                }

                // 方法5：检测全局代理端口
                try {
                    var globalProxyPort = shell("settings get global global_http_proxy_port", true);
                    TestLogModule.log("全局代理端口检测结果: " + JSON.stringify(globalProxyPort), "INFO");
                    if (globalProxyPort && globalProxyPort.code === 0 && globalProxyPort.result) {
                        var portResult = globalProxyPort.result.toString().trim();
                        TestLogModule.log("全局代理端口原始结果: '" + portResult + "'", "INFO");
                        if (portResult && portResult !== "" && portResult !== "null" && portResult !== "0" && portResult !== "-1") {
                            TestLogModule.log("检测到全局代理端口: " + portResult, "WARN");
                            detected = true;
                        }
                    }
                } catch (e) {
                    TestLogModule.log("全局代理端口检测异常: " + e.message, "ERROR");
                }

                // 方法6：检测WiFi代理设置（需要更高权限）
                try {
                    var wifiProxy = shell("settings get global http_proxy", true);
                    TestLogModule.log("WiFi代理检测结果: " + JSON.stringify(wifiProxy), "INFO");
                } catch (e) {
                    TestLogModule.log("WiFi代理检测异常: " + e.message, "ERROR");
                }

                // 方法7：检测网络配置文件
                try {
                    var netConfig = shell("getprop | grep proxy", true);
                    TestLogModule.log("网络配置代理检测结果: " + JSON.stringify(netConfig), "INFO");
                    if (netConfig && netConfig.code === 0 && netConfig.result) {
                        var configResult = netConfig.result.toString().trim();
                        TestLogModule.log("网络配置代理原始结果: '" + configResult + "'", "INFO");
                        if (configResult && configResult !== "" && configResult.includes("proxy")) {
                            TestLogModule.log("检测到网络配置中的代理设置", "WARN");
                            detected = true;
                        }
                    }
                } catch (e) {
                    TestLogModule.log("网络配置代理检测异常: " + e.message, "ERROR");
                }

                // 方法8：检测环境变量中的代理
                try {
                    var envProxy = shell("env | grep -i proxy", true);
                    TestLogModule.log("环境变量代理检测结果: " + JSON.stringify(envProxy), "INFO");
                    if (envProxy && envProxy.code === 0 && envProxy.result) {
                        var envResult = envProxy.result.toString().trim();
                        TestLogModule.log("环境变量代理原始结果: '" + envResult + "'", "INFO");
                        if (envResult && envResult !== "") {
                            TestLogModule.log("检测到环境变量中的代理设置", "WARN");
                            detected = true;
                        }
                    }
                } catch (e) {
                    TestLogModule.log("环境变量代理检测异常: " + e.message, "ERROR");
                }

                if (detected) {
                    TestLogModule.log("检测到网络代理设置", "WARN");
                } else {
                    TestLogModule.log("未检测到网络代理设置", "INFO");
                }
                
                return detected;
            } catch (e) {
                TestLogModule.log("检测代理设置时发生严重错误: " + e.message, "ERROR");
                return false;
            }
        }
    };
})();

// 测试函数
function testPacketCaptureDetection() {
    console.log("=== 开始抓包检测测试 ===");
    
    try {
        // 执行抓包检测
        var detected = PacketCaptureDetector.detectPacketCapture();
        
        if (detected) {
            console.log("❌ 检测结果：发现网络抓包活动");
            toast("检测到网络抓包活动！");
            
            // 显示详细信息对话框
            dialogs.build({
                title: "⚠️ 抓包工具检测结果",
                content: "检测到设备运行了抓包工具，已尝试自动结束相关进程。\n\n检测到的工具包括：\n• Charles、Fiddler、Reqable等桌面抓包工具\n• HttpCanary等移动端抓包工具\n• 其他网络抓包分析工具\n\n请查看日志了解进程结束情况。",
                positive: "确定"
            }).show();
            
        } else {
            console.log("✅ 检测结果：未发现网络抓包活动");
            toast("未检测到网络抓包活动");
            
            dialogs.build({
                title: "✅ 抓包工具检测结果",
                content: "未检测到抓包工具，设备环境安全。",
                positive: "确定"
            }).show();
        }
        
    } catch (e) {
        console.error("测试过程中发生错误: " + e.message);
        toast("测试失败: " + e.message);
    }
    
    console.log("=== 抓包检测测试完成 ===");
}

// 测试访问百度前的抓包检测
function testBaiduAccess() {
    console.log("=== 开始测试访问百度 ===");
    TestLogModule.log("=== 模拟访问百度前的安全检测 ===", "INFO");
    
    try {
        // 使用安全访问功能访问百度
        var result = PacketCaptureDetector.safeRequest("https://www.baidu.com");
        
        if (result.success) {
            TestLogModule.log("✅ 百度访问成功", "INFO");
            var content = result.data.substring(0, 200) + "...";
            TestLogModule.log("百度页面内容预览: " + content, "INFO");
            
            dialogs.build({
                title: "✅ 百度访问成功",
                content: "抓包检测通过，成功访问百度。\n\n页面大小: " + result.data.length + " 字符",
                positive: "确定"
            }).show();
            
        } else {
            TestLogModule.log("❌ 百度访问失败: " + result.error, "ERROR");
            
            dialogs.build({
                title: "❌ 百度访问失败",
                content: "访问失败原因: " + result.error + "\n\n如果是因为检测到抓包，请关闭抓包工具后重试。",
                positive: "确定"
            }).show();
        }
        
    } catch (e) {
        TestLogModule.log("百度访问测试异常: " + e.message, "ERROR");
        toast("测试失败: " + e.message);
    }
    
    console.log("=== 百度访问测试完成 ===");
}

// 测试快速抓包检测
function testQuickDetection() {
    console.log("=== 开始快速抓包检测测试 ===");
    TestLogModule.log("=== 快速抓包检测测试 ===", "INFO");
    
    try {
        // 测试快速抓包工具检测
        var captureDetected = PacketCaptureDetector.quickCaptureCheck();
        TestLogModule.log("快速抓包工具检测结果: " + (captureDetected ? "检测到抓包工具" : "未检测到抓包工具"), "INFO");
        
        dialogs.build({
            title: captureDetected ? "⚠️ 快速检测结果" : "✅ 快速检测结果",
            content: "抓包工具检测: " + (captureDetected ? "发现抓包工具" : "正常") + "\n\n" +
                    "总体结果: " + (captureDetected ? "存在抓包风险" : "环境安全"),
            positive: "确定"
        }).show();
        
    } catch (e) {
        TestLogModule.log("快速检测测试异常: " + e.message, "ERROR");
        toast("测试失败: " + e.message);
    }
    
    console.log("=== 快速抓包检测测试完成 ===");
}

// 测试进程检测
function testProcessDetection() {
    console.log("=== 开始进程检测测试 ===");
    TestLogModule.log("=== 专门进程检测测试 ===", "INFO");
    
    try {
        // 只测试进程检测
        var processDetected = PacketCaptureDetector.checkCaptureProcesses();
        
        dialogs.build({
            title: processDetected ? "⚠️ 进程检测结果" : "✅ 进程检测结果",
            content: processDetected ? 
                "检测到抓包工具进程！\n\n请查看日志了解具体检测到的工具。" :
                "未检测到抓包工具进程，系统环境安全。",
            positive: "确定"
        }).show();
        
    } catch (e) {
        TestLogModule.log("进程检测测试异常: " + e.message, "ERROR");
        toast("测试失败: " + e.message);
    }
    
    console.log("=== 进程检测测试完成 ===");
}

// 连续检测测试（复现问题）
function testDoubleDetection() {
    console.log("=== 开始连续检测测试 ===");
    TestLogModule.log("=== 连续检测测试 - 复现问题 ===", "INFO");
    
    TestLogModule.log("第一次检测开始...", "INFO");
    
    try {
        // 第一次检测
        var firstDetected = PacketCaptureDetector.checkCaptureProcesses();
        TestLogModule.log("第一次检测结果: " + (firstDetected ? "检测到抓包工具" : "未检测到抓包工具"), "INFO");
        
        // 等待5秒后进行第二次检测
        setTimeout(function() {
            TestLogModule.log("等待5秒后，开始第二次检测...", "INFO");
            
            try {
                var secondDetected = PacketCaptureDetector.checkCaptureProcesses();
                TestLogModule.log("第二次检测结果: " + (secondDetected ? "检测到抓包工具" : "未检测到抓包工具"), "INFO");
                
                // 分析结果
                if (firstDetected && secondDetected) {
                    TestLogModule.log("⚠️ 问题确认: 第一次结束进程后，第二次仍然检测到抓包工具", "WARN");
                    
                    dialogs.build({
                        title: "⚠️ 连续检测结果",
                        content: "问题确认：\n第一次检测：发现抓包工具并强制结束\n第二次检测：仍然检测到抓包工具\n\n可能原因：\n• 抓包工具自动重启\n• 应用有守护进程\n• 需要手动彻底关闭",
                        positive: "确定"
                    }).show();
                    
                } else if (firstDetected && !secondDetected) {
                    TestLogModule.log("✅ 正常情况: 第一次结束进程成功，第二次未检测到", "INFO");
                    
                    dialogs.build({
                        title: "✅ 连续检测结果",
                        content: "检测正常：\n第一次检测：发现抓包工具并强制结束\n第二次检测：未检测到抓包工具\n\n强制结束功能工作正常！",
                        positive: "确定"
                    }).show();
                    
                } else if (!firstDetected && !secondDetected) {
                    TestLogModule.log("ℹ️ 两次检测都未发现抓包工具", "INFO");
                    
                    dialogs.build({
                        title: "ℹ️ 连续检测结果",
                        content: "两次检测都未发现抓包工具。\n\n请先启动抓包工具再进行测试。",
                        positive: "确定"
                    }).show();
                } else {
                    TestLogModule.log("🤔 异常情况: 第一次未检测到，第二次检测到", "WARN");
                }
                
            } catch (e) {
                TestLogModule.log("第二次检测异常: " + e.message, "ERROR");
            }
            
        }, 5000);
        
    } catch (e) {
        TestLogModule.log("第一次检测异常: " + e.message, "ERROR");
        toast("测试失败: " + e.message);
    }
    
    console.log("=== 连续检测测试启动完成，等待结果 ===");
}

// 测试网络请求前检测（模拟main.js中的逻辑）
function testNetworkRequestDetection() {
    console.log("=== 开始网络请求前检测测试 ===");
    TestLogModule.log("=== 网络请求前检测测试 ===", "INFO");
    
    try {
        TestLogModule.log("模拟发送网络请求前的检测逻辑...", "INFO");
        
        // 模拟main.js中NetworkModule.request的检测逻辑
        var captureDetected = PacketCaptureDetector.quickCaptureCheck();
        if (captureDetected) {
            TestLogModule.log("⚠️ 检测到抓包工具，网络请求被拒绝", "WARN");
            
            dialogs.build({
                title: "⚠️ 网络请求被阻止",
                content: "检测到抓包工具正在运行，网络请求已被阻止。\n\n这模拟了main.js中的实际行为：\n• 检测到抓包工具\n• 拒绝发送网络请求\n• 显示警告并退出应用",
                positive: "确定"
            }).show();
            
        } else {
            TestLogModule.log("✅ 抓包检测通过，可以安全发送网络请求", "INFO");
            
            dialogs.build({
                title: "✅ 网络请求检测通过",
                content: "未检测到抓包工具，网络请求可以正常发送。\n\n这模拟了main.js中的正常流程：\n• 检测通过\n• 继续发送网络请求\n• 应用正常运行",
                positive: "确定"
            }).show();
        }
        
    } catch (e) {
        TestLogModule.log("网络请求前检测异常: " + e.message, "ERROR");
        toast("检测失败: " + e.message);
    }
    
    console.log("=== 网络请求前检测测试完成 ===");
}

// 创建测试界面
ui.layout(
    <vertical>
        <text text="网络抓包检测测试" textSize="20sp" gravity="center" margin="20"/>
        <text text="选择测试类型：" gravity="center" margin="10"/>
        
        <horizontal margin="10">
            <button id="fullTestBtn" text="完整检测" layout_weight="1" margin="5"/>
            <button id="quickTestBtn" text="快速检测" layout_weight="1" margin="5"/>
        </horizontal>
        
        <horizontal margin="10">
            <button id="processTestBtn" text="进程检测" layout_weight="1" margin="5"/>
            <button id="baiduTestBtn" text="测试访问百度" layout_weight="1" margin="5"/>
        </horizontal>
        
        <horizontal margin="10">
            <button id="doubleTestBtn" text="连续检测测试" layout_weight="1" margin="5"/>
            <button id="networkTestBtn" text="网络请求检测" layout_weight="1" margin="5"/>
        </horizontal>
        
        <horizontal margin="10">
            <button id="clearLogBtn" text="清空日志" layout_weight="1" margin="5"/>
        </horizontal>
        
        <text text="检测日志：" textSize="16sp" margin="10"/>
        <ScrollView>
            <text id="logText" text="等待开始检测..." textSize="12sp" margin="10" maxLines="50"/>
        </ScrollView>
        
        <text text="测试说明：" textSize="14sp" margin="10"/>
        <text text="• 完整检测：全面检测抓包工具进程和端口" margin="5" textSize="11sp"/>
        <text text="• 快速检测：只检测常见抓包工具（适合请求前检测）" margin="5" textSize="11sp"/>
        <text text="• 进程检测：专门检测抓包工具进程（主要方法）" margin="5" textSize="11sp"/>
        <text text="• 测试访问百度：模拟实际业务请求前的检测" margin="5" textSize="11sp"/>
        <text text="• 连续检测测试：复现第二次检测仍发现抓包工具的问题" margin="5" textSize="11sp"/>
        <text text="• 网络请求检测：模拟main.js中每次网络请求前的检测" margin="5" textSize="11sp"/>
        <text text="• 所有检测过程都会在上方日志中显示" margin="5" textSize="11sp"/>
    </vertical>
);

// 绑定按钮事件
ui.fullTestBtn.click(function() {
    TestLogModule.clear();
    setTimeout(function() {
        testPacketCaptureDetection();
    }, 100);
});

ui.quickTestBtn.click(function() {
    TestLogModule.clear();
    setTimeout(function() {
        testQuickDetection();
    }, 100);
});

ui.processTestBtn.click(function() {
    TestLogModule.clear();
    setTimeout(function() {
        testProcessDetection();
    }, 100);
});

ui.baiduTestBtn.click(function() {
    TestLogModule.clear();
    setTimeout(function() {
        testBaiduAccess();
    }, 100);
});

ui.doubleTestBtn.click(function() {
    TestLogModule.clear();
    setTimeout(function() {
        testDoubleDetection();
    }, 100);
});

ui.networkTestBtn.click(function() {
    TestLogModule.clear();
    setTimeout(function() {
        testNetworkRequestDetection();
    }, 100);
});

ui.clearLogBtn.click(function() {
    TestLogModule.clear();
});

console.log("网络抓包检测测试脚本已加载，选择测试类型开始测试");